import argparse
from dotenv import load_dotenv

import os
import re
import rich
import rich.box
from rich.text import Text
from rich.prompt import Prompt
from rich.console import Console
from rich.panel import Panel
from rich.align import Align
import threading
import time
import cv2
import mss
import numpy as np
import imageio
import tkinter as tk
import shutil
import subprocess
from pathlib import Path

from run import create_agent
from utils.vis import logo, get_adjusted_window_size
from utils.logging_setting import set_logging

QUANTUM_PURPLE = "#7e4fff"
LogLevel_INFO = 20

load_dotenv(override=True)

class ScreenRecorder:
    def __init__(self, filename="screen_record.mp4", fps=10.0):
        self.filename = filename
        self.fps = fps
        self.sct = mss.mss()
        self._is_recording = False # 初始化 _is_recording 状态

        # 动态获取物理分辨率
        # 注意: monitors[0] 通常是所有显示器组合的虚拟屏幕，monitors[1] 是主显示器。
        # 如果你有多个显示器，可能需要调整索引或选择逻辑。
        if not self.sct.monitors:
            raise RuntimeError("无法检测到显示器。")
        monitor = self.sct.monitors[1] # 假设使用第一个物理显示器 (主显示器)

        # 使用 grab() 获取实际截图尺寸，因为 monitor 字典中的 width/height 可能不总是准确
        # 或者可能与 grab() 的行为略有不同
        temp_sct = self.sct.grab(monitor)
        self.width = temp_sct.width//2
        self.height = temp_sct.height//2

        self.monitor_area = {
            "top": monitor["top"],
            "left": monitor["left"],
            "width": self.width,
            "height": self.height,
            "mon": monitor.get("mon", 1) # mss 可能会使用 mon 键，尽管在 grab 中通常不需要
        }

        # 创建输出目录
        output_path = Path(filename)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # imageio 的 VideoWriter 初始化
        try:
            print(f"尝试使用 imageio 初始化视频写入器: {output_path}")
            print(f"参数: fps={fps}, codec='libx264', quality=8, pixelformat='yuv420p'")
            print(f"预期分辨率 (W x H): {self.width} x {self.height}")

            self.video_writer = imageio.get_writer(
                str(output_path),       # 文件路径
                fps=self.fps,           # 帧率
                codec='libx264',        # H.264 编码器，需要 ffmpeg
                quality=8,              # 质量 (imageio 对 libx264 通常0-10，10最高，实际映射到CRF)
                pixelformat='yuv420p',  # 像素格式，确保广泛兼容性
                # macro_block_size=None # 如果遇到宽度/高度不是16的倍数问题，可以尝试设置
            )
            print(f"imageio 视频写入器初始化成功。")

        except Exception as e:
            # imageio.get_writer 失败时会抛出异常 (例如 ffmpeg 未找到或参数错误)
            print(f"使用 imageio 初始化视频写入器失败: {e}")
            print("请确保已安装 FFmpeg (imageio-ffmpeg) 并且 'libx264' 编码器可用。")
            raise RuntimeError(f"视频写入器初始化失败: {e}") # 直接抛出 imageio 的错误

        self._is_recording = False # 确保在所有初始化后设置
        print(f"屏幕录制器初始化完成 | 文件: {output_path} | 帧率: {self.fps} | 分辨率: {self.width}x{self.height}")

    def start_recording(self):
        if self._is_recording:
            print("录制已经在进行中。")
            return

        self._is_recording = True
        print("屏幕录制开始...")
        try:
            while self._is_recording:
                frame_start_time = time.perf_counter()

                # 捕获屏幕
                screenshot = self.sct.grab(self.monitor_area)

                # 将 BGRA (mss 格式) 转换为 RGB (imageio/ffmpeg 更标准的输入格式)
                frame_np = np.array(screenshot, dtype=np.uint8)
                frame_rgb = cv2.cvtColor(frame_np, cv2.COLOR_BGRA2RGB)

                # 写入视频帧
                try:
                    self.video_writer.append_data(frame_rgb)
                except Exception as e:
                    print(f"写入帧数据时出错: {e}")
                    continue

                processing_time = time.perf_counter() - frame_start_time
                sleep_duration = (1.0 / self.fps) - processing_time
                if sleep_duration > 0:
                    time.sleep(sleep_duration)

        except KeyboardInterrupt:
            print("通过用户中断停止录制...")
        except Exception as e:
            print(f"录制过程中发生错误: {e}")
        finally:
            self.stop_recording()

    def stop_recording(self):
        if not self._is_recording:
            return
        self._is_recording=False
        time.sleep(1)
        self._is_recording = False
        print("屏幕录制停止中...")
        if hasattr(self, 'video_writer') and self.video_writer is not None:
            try:
                self.video_writer.close()
                print("视频写入器已关闭。")

                # 处理4倍速视频
                print("正在处理视频为4倍速...")
                output_path = Path(self.filename)
                speed_output_path = output_path.with_stem(f"{output_path.stem}_4x")

                try:
                    # 使用FFmpeg处理视频速度
                    cmd = [
                        'ffmpeg',
                        '-i', str(output_path),
                        '-filter:v', 'setpts=0.25*PTS',  # 视频速度4倍
                        '-filter:a', 'atempo=4.0',       # 音频速度4倍（如果有音频）
                        '-y',                            # 覆盖输出文件
                        str(speed_output_path)
                    ]
                    subprocess.run(cmd, check=True)
                    print(f"4倍速视频已保存至: {speed_output_path}")
                except Exception as e:
                    print(f"处理4倍速视频时出错: {e}")
                    print("请确保已安装FFmpeg并添加到系统PATH中。")

            except Exception as e:
                print(f"关闭视频写入器时出错: {e}")

    def __del__(self):
        self.stop_recording()
        if hasattr(self.sct, 'close'):
             self.sct.close()


def parse_md_to_plan(md_text):
    lines = md_text.strip().split("\n")
    task = []
    steps = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Check if line starts with number for step
        if re.match(r"^\d+\.", line):
            step = line.split(".", 1)[1].strip()
            steps.append(step)
        else:
            task.append(line)

    return {"task": "\n".join(task), "steps": steps}


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_id", type=str, default="o3-mini")
    parser.add_argument("--task_file", type=str, default=None)
    parser.add_argument("--recordings_root", type=str, default="./recordings")
    parser.add_argument("--recordings_dir", type=str, default=None)
    parser.add_argument("--headless", action="store_true")
    parser.add_argument("--storage_state", type=str, default=None)
    parser.add_argument(
        "--window_position",
        type=lambda s: eval(f"dict({s})"),
        default={"width": 0, "height": 0},
        help="Window position as key-value pairs, e.g. 'width=100,height=200'",
    )
    parser.add_argument("--use_terminal_aware", action="store_true")
    return parser.parse_args()


def main():
    args = parse_args()
    # char_width is the terminal font width, which needs to be adjusted based on the actual font being used
    # Default value of 7.5 is used for most monospace fonts, but may need adjustment for different fonts
    window_size = get_adjusted_window_size(
        char_width=7.5, use_terminal_aware=args.use_terminal_aware
    )

    logo()

    if args.task_file is None:
        # TODO: logs
        agent = create_agent(
            model_id=args.model_id,
            window_size=window_size,
        )
        console = Console()
        content = Text()
        content.append("Welcome to Simplex AI!\n", style="bold white")
        content.append(
            "We are your dedicated AI Agent assistant, designed to solve complex challenges, analyze data, and provide intelligent insights across domains."
        )

        panel = Panel(
            Align(content, align="left"),
            border_style="white",
            padding=(1, 4),
            box=rich.box.ROUNDED,
        )
        input_prompt = Text("Please enter your question: ", style="bold white")
        console.print(panel)
        user_input = Prompt.ask(
            input_prompt,
            console=console,
            password=False,
            default=None,
            show_default=False,
            choices=None,
        )
        agent.run(user_input)
    else:
        with open(args.task_file, "r") as file:
            md_text = file.read()
        parsed_content = parse_md_to_plan(md_text)

        recordings_root = args.recordings_root
        filename = os.path.basename(args.task_file)

        if args.recordings_dir is not None:
            recordings_dir = args.recordings_dir
        else:
            task_name_without_ext = os.path.splitext(filename)[0]
            recordings_dir = os.path.join(recordings_root, task_name_without_ext)

        browser_log_dir = os.path.join(recordings_dir, "browser_logs")
        os.makedirs(recordings_dir, exist_ok=True)
        log_file = os.path.join(recordings_dir, "output.log")
        main_file = os.path.join(recordings_dir, "main.log")
        with open(log_file, "w") as f:
            f.write("")
        with open(main_file, "w") as f:
            f.write("")
        set_logging(log_file)

        agent = create_agent(
            model_id=args.model_id,
            browser_log_dir=browser_log_dir,
            window_size=window_size,
            window_position=args.window_position,
            sub_agents=["browser"],
            headless=args.headless,
            storage_state=args.storage_state,
        )
        agent.logger.register_log_file(main_file, log_file)

        parsed_content["task"] += f"\nPlease create file in {recordings_dir}"
        agent._get_plan_from_user(parsed_content)

        # 设置视频录制
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        output_filename = os.path.join(recordings_dir, f"recording_{timestamp}.mp4")
        recorder = ScreenRecorder(filename=output_filename, fps=10.0)

        # 启动录制线程
        recording_thread = threading.Thread(target=recorder.start_recording)
        recording_thread.start()

        try:
            cost = agent._run_to_completion_cost()
            print(cost)
        except Exception as e:
            print(f"An error occurred during agent run: {e}")
        finally:
            if recorder is not None:
                time.sleep(1)
                recorder.stop_recording()
                if 'recording_thread' in locals() and recording_thread.is_alive():
                    recording_thread.join(timeout=2)


if __name__ == "__main__":
    main()
