import re
import json
from datetime import datetime
from typing import List, Any, Type
from pydantic import BaseModel, Field, create_model
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, BrowserConfig
from concurrent.futures import ThreadPoolExecutor
from threading import Lock

import pandas as pd
from tqdm import tqdm
from openpyxl import load_workbook
from scrapegraphai.graphs import SmartScraperGraph


async def schema_gen_with_list(
    llm, user_prompt: str, url: str = "", html: str = ""
) -> tuple[Type[BaseModel], Type[BaseModel]]:
    """
    Generate both an item schema and its corresponding list schema.

    Args:
        user_prompt: Description of what data structure/schema to generate

    Returns:
        Tuple of (ItemSchema, ListItemSchema)
    """
    # Generate the item schema
    item_schema, content = await schema_gen(llm, user_prompt, url=url, html=html)

    # Generate the list schema
    list_schema = create_list_schema(item_schema)

    return item_schema, list_schema, content


async def crawl_url(url: str = "", html: str = "") -> str:
    browser_cfg = BrowserConfig(
        headless=False,
        viewport_width=1280,
        viewport_height=720,
    )

    crawler_cfg = CrawlerRunConfig(
        magic=True,
        simulate_user=True,
        override_navigator=True,
        page_timeout=30000,
        scan_full_page=True,
    )

    if html:  # prioritize html
        url = f"raw:{html}"

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        result = await crawler.arun(
            url=url,
            config=crawler_cfg,
        )
    return result.markdown


async def schema_gen(
    llm, user_prompt: str, url: str = "", html: str = ""
) -> Type[BaseModel]:
    """
    Generate a single Pydantic BaseModel class using GPT-4o based on user prompt.

    Args:
        user_prompt: Description of what data structure/schema to generate

    Returns:
        A single BaseModel class
    """

    if url or html:
        content = await crawl_url(url=url, html=html)
        print(f"len of markdown: {len(content)}")
        # Limit content size and clean it
        content_preview = content[:30000]  # Reduced size
        user_message = f"""Based on the content of the webpage: {content_preview}, """
    else:
        content = html
        user_message = ""

    # Create the system prompt for schema generation
    system_prompt = """You are an expert Python developer specializing in Pydantic schemas.
Your task is to generate field definitions for a single Pydantic BaseModel class based on user requirements.

Guidelines:
1. Return ONLY a JSON object with a single schema definition
2. Use proper type hints (str, int, float, bool, etc.)
3. Add descriptive descriptions for each field
4. Follow Python naming conventions (snake_case for fields)

Return format should be a JSON object like this:
{
  "schema_name": "ItemSchema",
  "fields": {
    "field_name": {"type": "str", "description": "Description of the field"},
    "field_number": {"type": "int", "description": "A number field"},
  }
}

Supported types: str, int, float, bool"""

    # Create the user prompt
    user_message += f"""Generate a single schema definition for: {user_prompt}."""
    for _ in range(5):
        try:
            # Call GPT-4o to generate the schema
            response = llm.invoke(
                [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message},
                ]
            )

            generated_content = response.content.strip()
            print(generated_content)

            # Clean up the response (remove markdown code blocks if present)
            if generated_content.startswith("```json"):
                generated_content = generated_content[7:]
            if generated_content.startswith("```"):
                generated_content = generated_content[3:]
            if generated_content.endswith("```"):
                generated_content = generated_content[:-3]

            # Parse the JSON response
            schema_def = json.loads(generated_content.strip())

            # Create the single BaseModel class
            schema_name = schema_def["schema_name"]
            fields = {}

            for field_name, field_def in schema_def["fields"].items():
                field_type = _parse_simple_field_type(field_def["type"])
                fields[field_name] = (
                    field_type,
                    Field(description=field_def["description"]),
                )

            # Create the model using create_model
            model_class = create_model(schema_name, **fields)

            return model_class, content
        except Exception as e:
            print(e)


def _parse_simple_field_type(type_str: str) -> Any:
    """
    Parse field type string and return the appropriate Python type for simple schemas.

    Args:
        type_str: String representation of the type (e.g., "str")

    Returns:
        The appropriate Python type
    """

    # Handle basic types
    if type_str == "str":
        return str
    elif type_str == "int":
        return int
    elif type_str == "float":
        return float
    elif type_str == "bool":
        return bool
    elif type_str == "list":
        return list

    # Handle List types (simple lists only)
    if type_str.startswith("List[") and type_str.endswith("]"):
        inner_type = type_str[5:-1]
        return List[_parse_simple_field_type(inner_type)]

    # Default to str if unknown
    return str


def create_list_schema(
    item_schema: Type[BaseModel],
    list_field_name: str = None,
    list_schema_name: str = None,
) -> Type[BaseModel]:
    """
    Create a list container schema for a given item schema.

    Args:
        item_schema: The BaseModel class to create a list for
        list_field_name: Name of the list field (default: pluralized item name)
        list_schema_name: Name of the list schema class (default: List + item schema name)

    Returns:
        A BaseModel class containing a list of the item schema
    """
    # Generate default names if not provided
    item_name = item_schema.__name__

    if list_schema_name is None:
        list_schema_name = f"List{item_name}"

    if list_field_name is None:
        # Simple pluralization: remove 'Schema' and add 's'
        base_name = item_name.replace("Schema", "").lower()
        list_field_name = f"{base_name}s"

    # Create the list schema
    list_schema = create_model(
        list_schema_name,
        **{
            list_field_name: (
                List[item_schema],
                Field(description=f"List of {item_name} objects"),
            )
        },
    )

    return list_schema


def scrapegraph_chunk(chunk_data):
    llm, list_schema, list_name, field_names, prompt, idx, cnt = chunk_data
    print(f"Thread processing chunk {idx} (size: {len(cnt)} chars)")

    graph_config = {
        "llm": {
            "model_instance": llm,
            "model_tokens": 128000,
        },
        "verbose": False,
        "headless": True,
    }
    local_data = []
    try:
        smart_scraper_graph = SmartScraperGraph(
            prompt=prompt,
            source=cnt,
            config=graph_config,
            schema=list_schema,
        )

        response = smart_scraper_graph.run()

        # Convert repositories to list of dictionaries
        if response and list_name in response and response[list_name]:
            for repo in response[list_name]:
                repo_dict = {key: repo.get(key, "") for key in field_names}
                local_data.append(repo_dict)

        print(f"items collected in {idx}th chunk: {len(local_data)}")

    except Exception as e:
        error_msg = str(e)
        print(f"Error processing chunk {error_msg}")

        subcnts = []
        substep = 40000  # Reduced chunk size to avoid hitting limits
        for i in range(0, len(cnt), substep):
            chunk = cnt[i : i + substep]
            subcnts.append(chunk)

        for mini_chunk in subcnts:
            # Try to process a smaller, more cleaned version
            try:
                if (
                    len(mini_chunk) > 100
                ):  # Only process if there's meaningful content left
                    print("Trying with heavily cleaned mini-chunk...")
                    smart_scraper_graph = SmartScraperGraph(
                        prompt=prompt,
                        source=mini_chunk,
                        config=graph_config,
                        schema=list_schema,
                    )
                    response = smart_scraper_graph.run()

                    if response and list_name in response and response[list_name]:
                        for repo in response[list_name]:
                            repo_dict = {key: repo.get(key, "") for key in field_names}
                            local_data.append(repo_dict)
                        print(
                            f"Successfully processed mini-chunk. Items added: {len(local_data)}"
                        )

            except Exception as e2:
                print(f"Mini-chunk processing also failed: {e2}")

    return local_data


async def scrapegraph_mp(
    llm,
    list_schema,
    field_names,
    prompt,
    url="",
    html="",
    chunk_size=200000,
    max_workers=7,
):
    """
    Multi-threaded version of test_scrapegraph for faster processing.

    Args:
        list_schema: Pydantic schema for the list container
        field_names: List of field names to extract
        prompt: Extraction prompt
        url: URL to scrape (optional)
        html: HTML content to process (optional)
        chunk_size: Size of each chunk for processing
        max_workers: Maximum number of threads to use
    """
    prompt += "\n Ensure all data on the webpage is extracted"

    list_name = ""
    for name in list_schema.model_fields.keys():
        list_name = name
        break
    # import pdb
    # pdb.set_trace()
    if html:  # prioritize html
        cnts = split_chunk(html, chunk_size)
    else:
        content = crawl_url(url=url)
        cnts = split_chunk(content, chunk_size)

    total_data = [None] * len(cnts)
    print(f"Processing {len(cnts)} chunks with {max_workers} threads")

    # Thread-safe data collection
    data_lock = Lock()

    # Process chunks using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all chunks for processing
        future_to_chunk = {
            executor.submit(
                scrapegraph_chunk,
                (llm, list_schema, list_name, field_names, prompt, idx, cnt),
            ): idx
            for idx, cnt in enumerate(cnts)
        }

        # Collect results as they complete
        for idx, future in tqdm(
            enumerate(future_to_chunk), total=len(cnts), desc="Processing chunks"
        ):
            chunk_idx = future_to_chunk[future]
            try:
                chunk_data = future.result()
                with data_lock:
                    # total_data.extend(chunk_data)
                    total_data[idx] = chunk_data  # 按原始顺序存储
                    total_num = sum([len(x) for x in total_data if x is not None])
                    print(f"Chunk {chunk_idx} completed. Total items: {total_num}")
            except Exception as e:
                print(f"Chunk {chunk_idx} failed with exception: {e}")

    total_data = [item for data in total_data if data is not None for item in data]

    # Save results to Excel
    df = pd.DataFrame(total_data)

    # Save to Excel
    excel_filename = f'results/{list_schema.__name__}_{datetime.now().strftime("%Y-%m-%d_%H-%M-%S")}.xlsx'
    df.to_excel(excel_filename, index=False, engine="openpyxl")

    # Auto-adjust column widths
    workbook = load_workbook(excel_filename)
    worksheet = workbook.active

    # Auto-adjust column widths based on content
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter

        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except Exception:
                pass

        # Add some padding and set the column width
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        worksheet.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook with adjusted column widths
    workbook.save(excel_filename)
    workbook.close()

    print(f"\nData saved to {excel_filename} with auto-adjusted column widths")
    print(f"Total repositories saved: {len(total_data)}")

    result = (
        f"Data saved to {excel_filename}. "
        + f"This xlsx contains {len(total_data)} pieces of data, each including {df.columns.tolist()}"
    )
    return result


def split_chunk(html, chunk_size):
    cnts = []
    # Split HTML into chunks
    paragraphs = re.split(r"\n\n\n|\n\u200b\n\u200b\n", html)

    chunks = []
    for para in paragraphs:
        if len(para) > chunk_size:
            for i in range(0, len(para), chunk_size):
                chunk = para[i : i + chunk_size]
                chunks.append(chunk)
        else:
            chunks.append(para)

    chunk = ""
    for para in chunks:
        if len(chunk) + len(para) > chunk_size:
            cnts.append(chunk)
            chunk = ""
        chunk += para + "\n\n\n"

    if chunk:
        cnts.append(chunk)

    return cnts


async def scroll_down(amount, browser_session):
    """
    (a) Use browser._scroll_container for container-aware scrolling.
    (b) If that JavaScript throws, fall back to window.scrollBy().
    """
    page = await browser_session.get_current_page()
    dy = amount or await page.evaluate("() => window.innerHeight")

    try:
        await browser_session._scroll_container(dy)
    except Exception as e:
        # Hard fallback: always works on root scroller
        await page.evaluate("(y) => window.scrollBy(0, y)", dy)
        print("Smart scroll failed; used window.scrollBy fallback", exc_info=e)

    amount_str = f"{amount} pixels" if amount is not None else "one page"
    msg = f"🔍 Scrolled down the page by {amount_str}"
    # print(msg)
    return msg
