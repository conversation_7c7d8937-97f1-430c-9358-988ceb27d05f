#!/usr/bin/env python
# coding=utf-8

from typing import Dict, Optional, Any
import logging
import asyncio

from langchain_openai import ChatOpenAI

import os
from dotenv import load_dotenv

# Add: import rich for beautified output and logging
from rich.console import Console
from rich.logging import <PERSON>Handler

from anotherme.parallel.utils import schema_gen_with_list, scrapegraph_mp, scroll_down


# Set up rich logging
console = Console()
logging.basicConfig(
    level="INFO",
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True)],
)
logger = logging.getLogger("ParalleAgent")


class Parallel_Agent:
    """
    Independent agent specialized for parallel extract struction informantion from webpage
    """

    def __init__(
        self,
        llm,
        max_steps: int = 10,
        name: Optional[str] = None,
        description: Optional[str] = None,
        inputs: Optional[Dict[str, Any]] = None,
        sources: Dict[str, Any] = None,
    ):
        self.agent_name = self.__class__.__name__
        self.llm = llm

        self.name = name
        self.description = description
        self.inputs = inputs

        self.max_steps = max_steps
        self.sources = sources

    async def run(self, task: str, source: str = "browser") -> str:
        if source == "browser":
            source_agent = self.sources[source]

            scroll_info = await scroll_down(
                amount=1200000, browser_session=source_agent.browser_session
            )
            print(scroll_info)
            await asyncio.sleep(5)
            page = await source_agent.browser_session.get_current_page()
            try:
                await page.wait_for_load_state(state="networkidle", timeout=20000)
            except Exception as e:
                print(e)

            html = await page.content()
            import pdb

            pdb.set_trace()
            item_schema, list_schema, content = await schema_gen_with_list(
                self.llm, user_prompt=task, html=html
            )

            field_names = []
            print(f"Generated model: {item_schema.__name__}")
            print("Fields:")
            for field_name, field_info in item_schema.model_fields.items():
                field_names.append(field_name)
                print(
                    f"  {field_name}: {field_info.annotation} - {field_info.description}"
                )
            # import pdb
            # pdb.set_trace()
            # content = content[:200000]
            result = await scrapegraph_mp(
                self.llm, list_schema, field_names, task, html=content, max_workers=7
            )

        print(result)
        logger.info(result)
        return result

    async def __call__(self, task: str, source: str = "browser"):
        report = await self.run(task, source=source)
        answser = f"Here is the final answer from your managed agent {report}"
        return answser


async def main(llm_model_instance, browseruse_agent):
    initial_actions = [
        {
            "go_to_url": {
                "url": "https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=US&is_targeted_country=false&media_type=all&q=lamp&search_type=keyword_unordered"
            }
        }
    ]
    initial_actions = browseruse_agent._convert_initial_actions(initial_actions)
    await browseruse_agent.browser_session.get_current_page()
    result = await browseruse_agent.multi_act(
        initial_actions, check_for_new_elements=False
    )
    browseruse_agent.state.last_result = result

    sources = {"browser": browseruse_agent}
    # Initialize the agent
    agent = Parallel_Agent(
        llm=llm_model_instance,
        max_steps=10,
        sources=sources,
    )

    # Run the agent to execute a task
    task = "extract the blogger link, first sentence of advertising content, and product link from all the advertisement tags on the webpage."

    print(f"Executing task: {task}")
    result = await agent(task)

    print("\nTask completed with result:")
    print(result)


if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)

    load_dotenv()
    llm_model_instance = ChatOpenAI(
        model="google/gemini-2.5-flash-preview-05-20",
        api_key=os.getenv("OPENROUTER_API_KEY"),
        base_url=os.getenv("OPENROUTER_API_BASE"),
    )

    from anotherme.browser_use.browser_use_agent import BrowserUseAgent

    browseruse_agent = BrowserUseAgent(
        task="",
        llm=llm_model_instance,
        name="",
        description="",
        inputs="",
        log_dir="./recordings/example/browser_logs",
        window_size={"width": 1920, "height": 1080},
        window_position={"width": 0, "height": 0},
        headless=False,
        storage_state="local_dev/data/fb.json",
    )
    asyncio.run(main(llm_model_instance, browseruse_agent))
