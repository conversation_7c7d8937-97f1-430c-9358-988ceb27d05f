import os
import asyncio

from dotenv import load_dotenv

from anotherme.run import create_agent
from anotherme.utils.vis import get_adjusted_window_size
from anotherme.utils.logging_setting import set_logging

import sys

sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "anotherme"))
)


load_dotenv(override=True)

recordings_root = "./recordings"
model_id = "gpt-4o"

recordings_dir = os.path.join(recordings_root, "example")
browser_log_dir = os.path.join(recordings_dir, "browser_logs")
os.makedirs(recordings_dir, exist_ok=True)
log_file = os.path.join(recordings_dir, "output.log")
main_file = os.path.join(recordings_dir, "main.log")
with open(log_file, "w") as f:
    f.write("")
with open(main_file, "w") as f:
    f.write("")
set_logging(log_file)

window_size = get_adjusted_window_size(char_width=7.5, use_terminal_aware=False)

agent = create_agent(
    model_id=model_id,
    browser_log_dir=browser_log_dir,
    window_size=window_size,
    sub_agents=["browser"],
)
agent.logger.register_log_file(main_file, log_file)


async def main():
    await agent.managed_agents["browseruse_agent"].excute_task(
        task="确认图片中的商品是否在1688上售卖，图片链接：https://cbu01.alicdn.com/img/ibank/O1CN01zz3SBw1ScnJe1RYHA_!!*************-0-cib.jpg"
    )

    await asyncio.sleep(3)
    await agent.managed_agents["browseruse_agent"].browser_context.__aexit__(
        None, None, None
    )
    # await agent.managed_agents['browseruse_agent'].browser.close()


asyncio.run(main())
